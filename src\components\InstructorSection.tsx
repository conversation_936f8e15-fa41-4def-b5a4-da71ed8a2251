const InstructorSection = () => {
  const instructors = [
    {
      id: 1,
      name: "BS. Nguyễn <PERSON>",
      title: "Chuyên gia <PERSON> mạ<PERSON>",
      experience: "15+ năm kinh nghiệm",
      hospital: "Bệnh viện Chợ Rẫy",
      rating: 4.9,
      students: 2500,
      courses: 8,
      avatar: "👨‍⚕️",
      specialties: ["<PERSON> mạch", "<PERSON><PERSON><PERSON><PERSON> áp", "Bệnh mạch vành"]
    },
    {
      id: 2,
      name: "TS. Trần Thị Bình",
      title: "Chuyên gia Nhi khoa",
      experience: "12+ năm kinh nghiệm",
      hospital: "Bệnh viện Nhi Đồng 1",
      rating: 4.8,
      students: 1800,
      courses: 6,
      avatar: "👩‍⚕️",
      specialties: ["<PERSON>hi khoa", "Dinh dưỡng trẻ em", "Phát triển trẻ"]
    },
    {
      id: 3,
      name: "PGS.TS. L<PERSON>",
      title: "Chuyên gia Da liễ<PERSON>",
      experience: "20+ năm kinh nghiệm",
      hospital: "Bệnh viện Da liễu TP.HCM",
      rating: 4.9,
      students: 3200,
      courses: 12,
      avatar: "👨‍⚕️",
      specialties: ["<PERSON> liễu", "Thẩm mỹ da", "Điều trị mụn"]
    },
    {
      id: 4,
      name: "BS. Phạm Thị Dung",
      title: "Chuyên gia Dinh dưỡng",
      experience: "10+ năm kinh nghiệm",
      hospital: "Viện Dinh dưỡng Quốc gia",
      rating: 4.7,
      students: 2100,
      courses: 9,
      avatar: "👩‍⚕️",
      specialties: ["Dinh dưỡng lâm sàng", "Giảm cân", "Dinh dưỡng thể thao"]
    },
    {
      id: 5,
      name: "BS. Hoàng Văn Em",
      title: "Chuyên gia Nội khoa",
      experience: "18+ năm kinh nghiệm",
      hospital: "Bệnh viện Bạch Mai",
      rating: 4.8,
      students: 2800,
      courses: 10,
      avatar: "👨‍⚕️",
      specialties: ["Nội khoa tổng quát", "Tiểu đường", "Gan mật"]
    },
    {
      id: 6,
      name: "TS. Vũ Thị Phương",
      title: "Chuyên gia Sản phụ khoa",
      experience: "14+ năm kinh nghiệm",
      hospital: "Bệnh viện Từ Dũ",
      rating: 4.9,
      students: 1900,
      courses: 7,
      avatar: "👩‍⚕️",
      specialties: ["Sản khoa", "Phụ khoa", "Chăm sóc thai kỳ"]
    }
  ];

  return (
    <section className="py-16 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Đội Ngũ Chuyên Gia Hàng Đầu
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Học từ những bác sĩ, chuyên gia y tế uy tín với nhiều năm kinh nghiệm thực tiễn
          </p>
        </div>

        {/* Instructors Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {instructors.map((instructor) => (
            <div
              key={instructor.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group"
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white relative">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-2xl">
                    {instructor.avatar}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-lg">{instructor.name}</h3>
                    <p className="text-blue-100 text-sm">{instructor.title}</p>
                  </div>
                </div>
                
                {/* Rating Badge */}
                <div className="absolute top-4 right-4 bg-yellow-400 text-blue-900 px-2 py-1 rounded-full text-xs font-bold">
                  ⭐ {instructor.rating}
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="mb-4">
                  <div className="text-sm text-gray-600 mb-2">
                    <span className="font-semibold">🏥</span> {instructor.hospital}
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    <span className="font-semibold">📅</span> {instructor.experience}
                  </div>
                </div>

                {/* Specialties */}
                <div className="mb-4">
                  <div className="text-sm font-semibold text-gray-700 mb-2">Chuyên môn:</div>
                  <div className="flex flex-wrap gap-1">
                    {instructor.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="font-bold text-blue-600">{instructor.students.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">Học viên</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-green-600">{instructor.courses}</div>
                    <div className="text-xs text-gray-500">Khóa học</div>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full mt-4 bg-blue-600 text-white py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200">
                  Xem Khóa Học
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center">
          <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg">
            Xem Tất Cả Chuyên Gia
          </button>
        </div>
      </div>
    </section>
  );
};

export default InstructorSection;
