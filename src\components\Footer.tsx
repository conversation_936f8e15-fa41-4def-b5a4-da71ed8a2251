import Link from 'next/link';
import Image from 'next/image';

const Footer = () => {
  return (
    <footer className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 text-white">
      {/* Top CTA Section */}
      <div className="bg-blue-600 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h3 className="text-2xl lg:text-3xl font-bold mb-4">
              🎓 Bắt Đầu Hành Trình Học Tập Ngay Hôm Nay!
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Tham gia cùng hàng nghìn học viên đã thành công với OME
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-yellow-400 text-blue-900 px-8 py-4 rounded-lg font-bold hover:bg-yellow-300 transition-colors">
                📞 Tư Vấn <PERSON>ễn <PERSON>: 028 9999 8899
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                💬 Chat Zalo
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block mb-6">
              <div className="w-32 h-10 relative">
                <Image
                  src="/assets/logoome.svg"
                  alt="OME Logo"
                  fill
                  className="object-contain brightness-0 invert"
                />
              </div>
            </Link>
            
            <p className="text-gray-300 mb-6 leading-relaxed">
              Nền tảng học trực tuyến hàng đầu về chăm sóc sức khỏe. 
              Cung cấp kiến thức chuyên môn từ các chuyên gia y tế uy tín.
            </p>
            
            <div className="flex space-x-4">
              <a
                href="https://facebook.com"
                className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              
              <a
                href="https://youtube.com"
                className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </a>
              
              <a
                href="https://linkedin.com"
                className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Liên Kết Nhanh</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/gioi-thieu" className="text-gray-300 hover:text-white transition-colors">
                  Giới thiệu
                </Link>
              </li>
              <li>
                <Link href="/khoa-hoc" className="text-gray-300 hover:text-white transition-colors">
                  Khóa học
                </Link>
              </li>
              <li>
                <Link href="/tin-tuc" className="text-gray-300 hover:text-white transition-colors">
                  Tin tức
                </Link>
              </li>
              <li>
                <Link href="/su-kien" className="text-gray-300 hover:text-white transition-colors">
                  Sự kiện
                </Link>
              </li>
              <li>
                <Link href="/lien-he" className="text-gray-300 hover:text-white transition-colors">
                  Liên hệ
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Hỗ Trợ</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/huong-dan" className="text-gray-300 hover:text-white transition-colors">
                  Hướng dẫn học
                </Link>
              </li>
              <li>
                <Link href="/cau-hoi-thuong-gap" className="text-gray-300 hover:text-white transition-colors">
                  Câu hỏi thường gặp
                </Link>
              </li>
              <li>
                <Link href="/chinh-sach" className="text-gray-300 hover:text-white transition-colors">
                  Chính sách
                </Link>
              </li>
              <li>
                <Link href="/dieu-khoan" className="text-gray-300 hover:text-white transition-colors">
                  Điều khoản sử dụng
                </Link>
              </li>
              <li>
                <Link href="/bao-mat" className="text-gray-300 hover:text-white transition-colors">
                  Chính sách bảo mật
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6">📞 Liên Hệ Ngay</h3>
            <div className="space-y-4">
              <div className="bg-blue-800 p-4 rounded-lg">
                <div className="text-yellow-400 font-bold text-lg mb-2">
                  🔥 HOTLINE 24/7
                </div>
                <a href="tel:02899998899" className="text-white text-xl font-bold hover:text-yellow-400 transition-colors">
                  028 9999 8899
                </a>
                <div className="text-blue-200 text-sm mt-1">
                  Tư vấn miễn phí - Hỗ trợ đăng ký
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span className="text-blue-400">📧</span>
                  <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </div>

                <div className="flex items-center space-x-3">
                  <span className="text-blue-400">💬</span>
                  <a href="https://zalo.me/ome-edu" className="text-gray-300 hover:text-white transition-colors">
                    Chat Zalo: @ome-edu
                  </a>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="text-blue-400 mt-1">📍</span>
                  <div className="text-gray-300">
                    Tầng 10, Tòa nhà ABC<br />
                    123 Nguyễn Huệ, Q.1, TP.HCM
                  </div>
                </div>
              </div>

              {/* Working Hours */}
              <div className="bg-green-800 p-3 rounded-lg">
                <div className="text-green-200 font-semibold text-sm mb-1">
                  ⏰ Giờ làm việc:
                </div>
                <div className="text-white text-sm">
                  T2-T6: 8:00 - 22:00<br />
                  T7-CN: 9:00 - 21:00
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 OME Việt Nam. Tất cả quyền được bảo lưu.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/chinh-sach" className="text-gray-400 hover:text-white text-sm transition-colors">
                Chính sách bảo mật
              </Link>
              <Link href="/dieu-khoan" className="text-gray-400 hover:text-white text-sm transition-colors">
                Điều khoản sử dụng
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
