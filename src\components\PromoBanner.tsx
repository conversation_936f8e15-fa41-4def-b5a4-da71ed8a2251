'use client';

import { useState } from 'react';

const PromoBanner = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="bg-gradient-to-r from-red-600 via-red-500 to-orange-500 text-white py-3 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-pulse"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-2xl animate-bounce">🎉</span>
              <div>
                <span className="font-bold text-lg">KHUYẾN MÃI ĐẶC BIỆT!</span>
                <span className="ml-2 text-yellow-200">
                  Gi<PERSON>m ngay 50% tất cả khóa học - Chỉ còn 3 ngày!
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2 bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <span className="text-sm font-semibold">⏰ Kết thúc trong:</span>
              <div className="flex space-x-1 text-yellow-300 font-bold">
                <span>02</span>
                <span>:</span>
                <span>23</span>
                <span>:</span>
                <span>45</span>
              </div>
            </div>
            
            <button className="bg-yellow-400 text-red-600 px-6 py-2 rounded-full font-bold hover:bg-yellow-300 transition-colors text-sm">
              ĐĂNG KÝ NGAY
            </button>
            
            <button
              onClick={() => setIsVisible(false)}
              className="text-white hover:text-yellow-200 transition-colors"
              aria-label="Đóng banner"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromoBanner;
