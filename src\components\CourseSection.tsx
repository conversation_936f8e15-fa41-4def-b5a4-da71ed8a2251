import Link from 'next/link';
import Image from 'next/image';

interface Course {
  id: string;
  title: string;
  description: string;
  instructor: string;
  duration: string;
  students: number;
  price: string;
  originalPrice?: string;
  image: string;
  category: string;
  rating: number;
  isHot?: boolean;
  isNew?: boolean;
  discount?: number;
}

const courses: Course[] = [
  {
    id: '1',
    title: '<PERSON><PERSON><PERSON><PERSON>m <PERSON> Khỏe <PERSON>àn <PERSON>',
    description: '<PERSON>h<PERSON><PERSON> học cung cấp kiến thức nền tảng về chăm sóc sức khỏe hàng ngày, dinh dưỡng và phòng bệnh từ chuyên gia.',
    instructor: 'BS. Nguyễn <PERSON>ăn <PERSON>',
    duration: '8 tuần',
    students: 2500,
    price: '990.000đ',
    originalPrice: '1.990.000đ',
    image: '/assets/course1.jpg',
    category: 'Sức khỏe tổng quát',
    rating: 4.9,
    isHot: true,
    discount: 50
  },
  {
    id: '2',
    title: '<PERSON><PERSON><PERSON><PERSON> Dưỡng <PERSON>â<PERSON>ng',
    description: 'Tìm hiểu sâu về dinh dưỡng trong điều trị và phòng ngừa các bệnh lý phổ biến từ chuyên gia hàng đầu.',
    instructor: 'TS. Trần Thị Bình',
    duration: '10 tuần',
    students: 1800,
    price: '1.290.000đ',
    originalPrice: '2.590.000đ',
    image: '/assets/course2.jpg',
    category: 'Dinh dưỡng',
    rating: 4.8,
    isNew: true,
    discount: 50
  },
  {
    id: '3',
    title: 'Chăm Sóc Người Cao Tuổi Chuyên Nghiệp',
    description: 'Kỹ năng chăm sóc chuyên nghiệp cho người cao tuổi và các bệnh lý thường gặp, được chứng nhận.',
    instructor: 'PGS.TS. Lê Minh Cường',
    duration: '12 tuần',
    students: 3200,
    price: '1.490.000đ',
    originalPrice: '2.990.000đ',
    image: '/assets/course3.jpg',
    category: 'Chăm sóc đặc biệt',
    rating: 4.9,
    discount: 50
  },
  {
    id: '4',
    title: 'Y Học Cổ Truyền Hiện Đại',
    description: 'Ứng dụng y học cổ truyền trong chăm sóc sức khỏe hiện đại với phương pháp khoa học.',
    instructor: 'BS. Phạm Thị Dung',
    duration: '8 tuần',
    students: 2100,
    price: '890.000đ',
    originalPrice: '1.790.000đ',
    image: '/assets/course4.jpg',
    category: 'Y học cổ truyền',
    rating: 4.7,
    discount: 50
  },
  {
    id: '5',
    title: 'Nội Khoa Tổng Quát Thực Hành',
    description: 'Khóa học nội khoa tổng quát với nhiều case study thực tế từ bệnh viện hàng đầu.',
    instructor: 'BS. Hoàng Văn Em',
    duration: '14 tuần',
    students: 2800,
    price: '1.690.000đ',
    originalPrice: '3.390.000đ',
    image: '/assets/course5.jpg',
    category: 'Nội khoa',
    rating: 4.8,
    isHot: true,
    discount: 50
  },
  {
    id: '6',
    title: 'Sản Phụ Khoa Chuyên Sâu',
    description: 'Chăm sóc sức khỏe phụ nữ và thai kỳ với kiến thức chuyên sâu từ chuyên gia.',
    instructor: 'TS. Vũ Thị Phương',
    duration: '10 tuần',
    students: 1900,
    price: '1.390.000đ',
    originalPrice: '2.790.000đ',
    image: '/assets/course6.jpg',
    category: 'Sản phụ khoa',
    rating: 4.9,
    isNew: true,
    discount: 50
  }
];

const CourseCard = ({ course }: { course: Course }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
      {/* Course Image */}
      <div className="relative h-48 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="w-16 h-16 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div className="text-sm font-medium">{course.category}</div>
          </div>
        </div>
        
        {/* Promotional Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {course.isHot && (
            <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
              🔥 HOT
            </div>
          )}
          {course.isNew && (
            <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
              ✨ MỚI
            </div>
          )}
          {course.discount && (
            <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold">
              -{course.discount}%
            </div>
          )}
        </div>
        
        {/* Rating */}
        <div className="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full flex items-center space-x-1">
          <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <span className="text-sm font-medium">{course.rating}</span>
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        <div className="mb-3">
          <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
            {course.category}
          </span>
        </div>
        
        <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          {course.title}
        </h3>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {course.description}
        </p>
        
        <div className="flex items-center text-sm text-gray-500 mb-4 space-x-4">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
            {course.instructor}
          </div>
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
              <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
            </svg>
            {course.duration}
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-red-600">{course.price}</span>
              {course.originalPrice && (
                <span className="text-sm text-gray-400 line-through">{course.originalPrice}</span>
              )}
            </div>
            {course.discount && (
              <div className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-bold">
                Tiết kiệm {course.discount}%
              </div>
            )}
          </div>

          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>👥 {course.students.toLocaleString()} học viên</span>
            <span>⏱️ {course.duration}</span>
          </div>
        </div>

        <div className="flex gap-2">
          <Link
            href={`/khoa-hoc/${course.id}`}
            className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 text-center"
          >
            Đăng Ký Ngay
          </Link>
          <button className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            ❤️
          </button>
        </div>
      </div>
    </div>
  );
};

const CourseSection = () => {
  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Khóa Học Nổi Bật
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Khám phá các khóa học chất lượng cao được thiết kế bởi đội ngũ chuyên gia y tế hàng đầu
          </p>
        </div>

        {/* Course Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
          {courses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/khoa-hoc"
            className="inline-flex items-center bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200"
          >
            Xem Tất Cả Khóa Học
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CourseSection;
