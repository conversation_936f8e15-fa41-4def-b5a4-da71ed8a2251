import Link from 'next/link';
import Image from 'next/image';

const HeroSection = () => {
  return (
    <section className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 py-16 lg:py-24 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute top-32 right-20 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-white bg-opacity-10 rounded-full"></div>
        <div className="absolute bottom-32 right-1/3 w-40 h-40 bg-white bg-opacity-5 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left text-white">
            <div className="mb-4">
              <span className="inline-block bg-yellow-400 text-blue-900 px-4 py-2 rounded-full text-sm font-semibold">
                🎓 Nền tảng học trực tuyến #1 Việt Nam
              </span>
            </div>

            <h1 className="text-3xl lg:text-5xl font-bold mb-6 leading-tight">
              HỌC ONLINE VỚI MẤT THẦY?
            </h1>

            <p className="text-lg lg:text-xl text-blue-100 mb-8 leading-relaxed">
              Tham gia cùng <span className="font-bold text-yellow-400">hơn 50.000</span> học viên
              đã tin tưởng và học tập cùng đội ngũ chuyên gia hàng đầu
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Link
                href="/khoa-hoc"
                className="bg-yellow-400 text-blue-900 px-8 py-4 rounded-lg font-bold hover:bg-yellow-300 transition-colors duration-200 text-center"
              >
                🚀 Khám Phá Ngay
              </Link>

              <Link
                href="/tu-van"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-700 transition-colors duration-200 text-center"
              >
                📞 Tư Vấn Miễn Phí
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 mt-8">
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-yellow-400 mb-1">50K+</div>
                <div className="text-sm text-blue-100">Học viên</div>
              </div>
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-yellow-400 mb-1">200+</div>
                <div className="text-sm text-blue-100">Khóa học</div>
              </div>
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-yellow-400 mb-1">50+</div>
                <div className="text-sm text-blue-100">Chuyên gia</div>
              </div>
            </div>
          </div>

          {/* Hero Image - Medical Professionals */}
          <div className="relative">
            <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden">
              {/* Placeholder for medical professionals image */}
              <div className="absolute inset-0 bg-gradient-to-br from-white to-blue-50 flex items-center justify-center">
                <div className="grid grid-cols-2 gap-4 p-8">
                  {/* Doctor 1 */}
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-3 bg-blue-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <div className="text-sm font-semibold text-gray-800">BS. Nguyễn A</div>
                    <div className="text-xs text-gray-600">Chuyên gia Tim mạch</div>
                  </div>

                  {/* Doctor 2 */}
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-3 bg-green-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <div className="text-sm font-semibold text-gray-800">TS. Trần B</div>
                    <div className="text-xs text-gray-600">Chuyên gia Nhi khoa</div>
                  </div>

                  {/* Doctor 3 */}
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-3 bg-purple-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <div className="text-sm font-semibold text-gray-800">PGS. Lê C</div>
                    <div className="text-xs text-gray-600">Chuyên gia Da liễu</div>
                  </div>

                  {/* Doctor 4 */}
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-3 bg-orange-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <div className="text-sm font-semibold text-gray-800">BS. Phạm D</div>
                    <div className="text-xs text-gray-600">Chuyên gia Dinh dưỡng</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Achievement Badge */}
            <div className="absolute -top-4 -right-4 bg-yellow-400 text-blue-900 p-3 rounded-full shadow-lg">
              <div className="text-center">
                <div className="text-lg font-bold">⭐</div>
                <div className="text-xs font-semibold">Top 1</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
