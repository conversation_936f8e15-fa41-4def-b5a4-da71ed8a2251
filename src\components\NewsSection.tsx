import Link from 'next/link';

const NewsSection = () => {
  const news = [
    {
      id: 1,
      title: "<PERSON> hướng chăm sóc sức khỏe 2024: <PERSON><PERSON><PERSON> nghệ AI trong y tế",
      excerpt: "Khám phá những ứng dụng mới nhất của trí tuệ nhân tạo trong việc chẩn đoán và điều trị bệnh...",
      category: "Công nghệ Y tế",
      date: "15/12/2024",
      readTime: "5 phút đọc",
      image: "📱",
      isHot: true
    },
    {
      id: 2,
      title: "Dinh dưỡng mùa đông: Tăng cường miễn dịch tự nhiên",
      excerpt: "Hướng dẫn chi tiết về chế độ ăn uống giúp tăng cường sức đề kháng trong mùa lạnh...",
      category: "Dinh dưỡng",
      date: "12/12/2024",
      readTime: "7 phút đọc",
      image: "🥗",
      isNew: true
    },
    {
      id: 3,
      title: "<PERSON>òng chống bệnh tim mạch: 10 thói quen hàng ngày",
      excerpt: "Những thói quen đơn giản nhưng hiệu quả giúp bảo vệ tim mạch khỏe mạnh...",
      category: "Tim mạch",
      date: "10/12/2024",
      readTime: "6 phút đọc",
      image: "❤️"
    },
    {
      id: 4,
      title: "Chăm sóc sức khỏe tâm thần trong thời đại số",
      excerpt: "Tác động của công nghệ đến sức khỏe tâm thần và cách cân bằng cuộc sống...",
      category: "Sức khỏe tâm thần",
      date: "08/12/2024",
      readTime: "8 phút đọc",
      image: "🧠"
    },
    {
      id: 5,
      title: "Vaccine COVID-19: Cập nhật mới nhất về hiệu quả",
      excerpt: "Thông tin cập nhật về các loại vaccine và khuyến cáo từ Bộ Y tế...",
      category: "Phòng chống dịch",
      date: "05/12/2024",
      readTime: "4 phút đọc",
      image: "💉"
    },
    {
      id: 6,
      title: "Yoga và thiền: Lợi ích cho sức khỏe toàn diện",
      excerpt: "Khám phá tác dụng tích cực của yoga và thiền đối với cả sức khỏe thể chất và tinh thần...",
      category: "Lối sống",
      date: "03/12/2024",
      readTime: "6 phút đọc",
      image: "🧘‍♀️"
    }
  ];

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Tin Tức Sức Khỏe
            </h2>
            <p className="text-lg text-gray-600">
              Cập nhật những thông tin y tế mới nhất từ các chuyên gia
            </p>
          </div>
          <Link
            href="/tin-tuc"
            className="hidden lg:block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Xem Tất Cả
          </Link>
        </div>

        {/* Featured Article */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          <div className="lg:col-span-2">
            <div className="relative bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl overflow-hidden text-white">
              <div className="p-8 lg:p-12">
                <div className="flex items-center space-x-2 mb-4">
                  <span className="bg-yellow-400 text-blue-900 px-3 py-1 rounded-full text-sm font-bold">
                    🔥 Nổi bật
                  </span>
                  <span className="text-blue-200 text-sm">{news[0].category}</span>
                </div>
                
                <h3 className="text-2xl lg:text-3xl font-bold mb-4 leading-tight">
                  {news[0].title}
                </h3>
                
                <p className="text-blue-100 mb-6 text-lg">
                  {news[0].excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-blue-200 text-sm">
                    <span>📅 {news[0].date}</span>
                    <span>⏱️ {news[0].readTime}</span>
                  </div>
                  
                  <Link
                    href={`/tin-tuc/${news[0].id}`}
                    className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                  >
                    Đọc Ngay
                  </Link>
                </div>
              </div>
              
              <div className="absolute top-4 right-4 text-6xl opacity-20">
                {news[0].image}
              </div>
            </div>
          </div>

          {/* Side Articles */}
          <div className="space-y-6">
            {news.slice(1, 3).map((article) => (
              <div key={article.id} className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-2xl">{article.image}</span>
                  <span className="text-blue-600 text-sm font-semibold">{article.category}</span>
                  {article.isNew && (
                    <span className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-bold">
                      MỚI
                    </span>
                  )}
                </div>
                
                <h4 className="font-bold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h4>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {article.excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="text-gray-500 text-xs">
                    {article.date} • {article.readTime}
                  </div>
                  <Link
                    href={`/tin-tuc/${article.id}`}
                    className="text-blue-600 font-semibold text-sm hover:text-blue-700"
                  >
                    Đọc thêm →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* More Articles Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news.slice(3).map((article) => (
            <div key={article.id} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-xl">{article.image}</span>
                <span className="text-blue-600 text-sm font-semibold">{article.category}</span>
              </div>
              
              <h4 className="font-bold text-gray-900 mb-2 line-clamp-2">
                {article.title}
              </h4>
              
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                {article.excerpt}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="text-gray-500 text-xs">
                  {article.date} • {article.readTime}
                </div>
                <Link
                  href={`/tin-tuc/${article.id}`}
                  className="text-blue-600 font-semibold text-sm hover:text-blue-700"
                >
                  Đọc →
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile View All Button */}
        <div className="text-center mt-8 lg:hidden">
          <Link
            href="/tin-tuc"
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Xem Tất Cả Tin Tức
          </Link>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
