import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "OME Việt Nam - Học Online Chăm Sóc Sức Khỏe Từ Chuyên Gia",
  description: "Nền tảng học trực tuyến hàng đầu về chăm sóc sức khỏe từ các chuyên gia y tế. Khóa học chất lượng cao, chứng chỉ uy tín.",
  keywords: "học online, chăm sóc sức khỏe, y tế, khóa học, chuyên gia",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <body
        className={`${inter.variable} font-sans antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
