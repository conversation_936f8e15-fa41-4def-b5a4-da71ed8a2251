# OME Việt Nam - Website Clone

A Next.js application that replicates the design and functionality of https://ome.edu.vn/, a Vietnamese online healthcare education platform.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15, TypeScript, and Tailwind CSS
- **Responsive Design**: Fully responsive layout that works on all devices
- **Vietnamese Localization**: Complete Vietnamese language support
- **Component-Based Architecture**: Modular and reusable React components
- **SEO Optimized**: Proper meta tags and semantic HTML structure
- **Interactive Elements**: Floating action buttons, smooth scrolling, and hover effects

## 📋 Components

### Core Components
- **PromoBanner**: Top promotional banner with countdown timer and special offers
- **Header**: Two-tier navigation with search functionality and user account options
- **HeroSection**: Blue gradient hero with medical professionals and compelling CTAs
- **InstructorSection**: Expert profiles with credentials and specialties
- **CourseSection**: Enhanced course cards with promotional badges and pricing
- **NewsSection**: Healthcare news and articles with featured content
- **FeaturesSection**: Why choose OME section with feature highlights
- **Footer**: Multi-section footer with prominent contact information
- **FloatingButtons**: Zalo, phone, and back-to-top floating action buttons

### Features Implemented
- ✅ Promotional banner with countdown timer
- ✅ Two-tier header with search and account options
- ✅ Blue gradient hero section matching original design
- ✅ Expert instructor profiles with photos and credentials
- ✅ Enhanced course cards with HOT/NEW badges and discounts
- ✅ News section with featured articles
- ✅ Prominent contact information and CTAs
- ✅ Floating action buttons with tooltips
- ✅ Smooth animations and hover effects
- ✅ Complete Vietnamese localization
- ✅ Mobile-responsive design
- ✅ Professional healthcare theme

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Font**: Inter (Google Fonts)
- **Icons**: Custom SVG icons
- **Development**: ESLint for code quality

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ome-clone
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
ome-clone/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
│       ├── PromoBanner.tsx
│       ├── Header.tsx
│       ├── HeroSection.tsx
│       ├── InstructorSection.tsx
│       ├── CourseSection.tsx
│       ├── NewsSection.tsx
│       ├── FeaturesSection.tsx
│       ├── Footer.tsx
│       └── FloatingButtons.tsx
├── public/
│   └── assets/
│       └── logoome.svg
├── package.json
├── postcss.config.mjs
└── tsconfig.json
```

## 🎨 Design Features

### Color Scheme
- Primary Blue: `#1e40af`
- Primary Green: `#059669`
- Primary Orange: `#ea580c`
- Text Dark: `#1f2937`
- Text Gray: `#6b7280`

### Responsive Breakpoints
- Mobile: `< 768px`
- Tablet: `768px - 1024px`
- Desktop: `> 1024px`

## 📱 Mobile Responsiveness

The application is fully responsive with:
- Collapsible mobile navigation
- Optimized touch targets
- Responsive grid layouts
- Mobile-friendly floating buttons
- Optimized typography scaling

## 🔧 Customization

### Adding New Courses
Edit the `courses` array in `src/components/CourseSection.tsx`

### Modifying Colors
Update the CSS variables in `src/app/globals.css`

## 📞 Contact Information

- **Phone**: 028 9999 8899
- **Email**: <EMAIL>
- **Address**: 123 Đường ABC, Quận 1, TP. Hồ Chí Minh

## 📄 License

This project is for educational purposes only. The original design belongs to OME Việt Nam.

---

Built with ❤️ using Next.js and Tailwind CSS
